#!/usr/bin/env node
/**
 * Main Demo Runner - Memory-Based AI Learning POC
 *
 * This demonstrates an AI agent that:
 * 1. Learns through conversation
 * 2. Reflects on learned knowledge
 * 3. Applies knowledge to new scenarios
 * 4. Explains its reasoning
 */
interface SuccessCriteria {
    readonly rulesLearned: number;
    readonly rulesAppliedCorrectly: number;
    readonly canArticulateReasoning: boolean;
    readonly modulesDecoupled: boolean;
}
interface DemoResult {
    readonly success: boolean;
    readonly criteria: SuccessCriteria;
    readonly summary: string;
    readonly details: {
        readonly trainingResults: any;
        readonly reflectionResult: any;
        readonly evaluationSummary: any;
    };
}
declare function runFullDemo(): Promise<DemoResult>;
export { runFullDemo };
//# sourceMappingURL=main.d.ts.map