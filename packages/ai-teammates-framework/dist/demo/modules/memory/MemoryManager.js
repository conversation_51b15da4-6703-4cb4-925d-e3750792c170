/**
 * Memory Manager - Core memory operations for the learning POC
 */
import { randomUUID } from 'crypto';
import { InMemoryStorage } from './InMemoryStorage.js';
// Create memory entry helper
const createMemoryEntry = (type, content, importance = 'medium', tags = [], metadata = {}) => ({
    id: randomUUID(),
    type,
    content,
    timestamp: new Date(),
    importance,
    tags,
    metadata
});
export class MemoryManager {
    storage;
    constructor(storage = new InMemoryStorage()) {
        this.storage = storage;
    }
    async storeTraining(content, isRule, metadata = {}) {
        const baseMemory = createMemoryEntry('training', content, 'high', ['training', 'policy'], metadata);
        const trainingMemory = {
            ...baseMemory,
            type: 'training',
            isRule,
            ruleCategory: isRule ? metadata.category : undefined,
            examples: metadata.examples
        };
        await this.storage.store(trainingMemory);
        return trainingMemory.id;
    }
    async storeReflection(insights, rules, confidence) {
        const baseMemory = createMemoryEntry('reflection', `Reflection: ${insights.join('; ')}`, 'critical', ['reflection', 'learning']);
        const reflectionMemory = {
            ...baseMemory,
            type: 'reflection',
            insights,
            extractedRules: rules,
            confidence
        };
        await this.storage.store(reflectionMemory);
        return reflectionMemory.id;
    }
    async storeConversation(content, importance = 'medium') {
        const conversationMemory = createMemoryEntry('conversation', content, importance, ['conversation']);
        await this.storage.store(conversationMemory);
        return conversationMemory.id;
    }
    async searchRelevant(query, limit = 5) {
        return this.storage.search({
            text: query,
            limit,
            minSimilarity: 0.1
        });
    }
    async getTrainingMemories() {
        const memories = await this.storage.list('training');
        return memories;
    }
    async getReflections() {
        const memories = await this.storage.list('reflection');
        return memories;
    }
    async getMemoryCount() {
        const allMemories = await this.storage.list();
        return allMemories.length;
    }
    async clear() {
        await this.storage.clear();
    }
    // Additional utility methods for the POC
    async getMemoriesByType(type) {
        return this.storage.list(type);
    }
    async getRecentMemories(limit = 10) {
        const allMemories = await this.storage.list();
        return allMemories.slice(0, limit);
    }
    async searchByTags(tags) {
        return this.storage.search({
            tags,
            limit: 20
        });
    }
    // Debug helpers
    async getMemoryStats() {
        const allMemories = await this.storage.list();
        const byType = {};
        const byImportance = {};
        for (const memory of allMemories) {
            byType[memory.type] = (byType[memory.type] || 0) + 1;
            byImportance[memory.importance] = (byImportance[memory.importance] || 0) + 1;
        }
        return {
            total: allMemories.length,
            byType,
            byImportance
        };
    }
}
// Factory function for easy creation
export const createMemoryManager = (storage) => {
    return new MemoryManager(storage);
};
//# sourceMappingURL=MemoryManager.js.map