{"version": 3, "file": "MemoryManager.js", "sourceRoot": "", "sources": ["../../../../demo/modules/memory/MemoryManager.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AAUpC,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AAEvD,6BAA6B;AAC7B,MAAM,iBAAiB,GAAG,CACxB,IAAyB,EACzB,OAAe,EACf,aAA+B,QAAQ,EACvC,OAA0B,EAAE,EAC5B,WAAoC,EAAE,EACzB,EAAE,CAAC,CAAC;IACjB,EAAE,EAAE,UAAU,EAAE;IAChB,IAAI;IACJ,OAAO;IACP,SAAS,EAAE,IAAI,IAAI,EAAE;IACrB,UAAU;IACV,IAAI;IACJ,QAAQ;CACT,CAAC,CAAC;AAEH,MAAM,OAAO,aAAa;IACJ;IAApB,YAAoB,UAAyB,IAAI,eAAe,EAAE;QAA9C,YAAO,GAAP,OAAO,CAAuC;IAAG,CAAC;IAEtE,KAAK,CAAC,aAAa,CACjB,OAAe,EACf,MAAe,EACf,WAAoC,EAAE;QAEtC,MAAM,UAAU,GAAG,iBAAiB,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC;QACpG,MAAM,cAAc,GAAmB;YACrC,GAAG,UAAU;YACb,IAAI,EAAE,UAAmB;YACzB,MAAM;YACN,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAkB,CAAC,CAAC,CAAC,SAAS;YAC9D,QAAQ,EAAE,QAAQ,CAAC,QAA6B;SACjD,CAAC;QAEF,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACzC,OAAO,cAAc,CAAC,EAAE,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,QAA2B,EAC3B,KAAwB,EACxB,UAAkB;QAElB,MAAM,UAAU,GAAG,iBAAiB,CAClC,YAAY,EACZ,eAAe,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EACpC,UAAU,EACV,CAAC,YAAY,EAAE,UAAU,CAAC,CAC3B,CAAC;QACF,MAAM,gBAAgB,GAAqB;YACzC,GAAG,UAAU;YACb,IAAI,EAAE,YAAqB;YAC3B,QAAQ;YACR,cAAc,EAAE,KAAK;YACrB,UAAU;SACX,CAAC;QAEF,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAC3C,OAAO,gBAAgB,CAAC,EAAE,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,OAAe,EACf,aAA+B,QAAQ;QAEvC,MAAM,kBAAkB,GAAG,iBAAiB,CAC1C,cAAc,EACd,OAAO,EACP,UAAU,EACV,CAAC,cAAc,CAAC,CACjB,CAAC;QAEF,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAC7C,OAAO,kBAAkB,CAAC,EAAE,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,KAAa,EAAE,KAAK,GAAG,CAAC;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACzB,IAAI,EAAE,KAAK;YACX,KAAK;YACL,aAAa,EAAE,GAAG;SACnB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,OAAO,QAAqC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACvD,OAAO,QAAuC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9C,OAAO,WAAW,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;IAED,yCAAyC;IACzC,KAAK,CAAC,iBAAiB,CAAC,IAAyB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,KAAK,GAAG,EAAE;QAChC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9C,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,IAAuB;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACzB,IAAI;YACJ,KAAK,EAAE,EAAE;SACV,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB;IAChB,KAAK,CAAC,cAAc;QAKlB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAE9C,MAAM,MAAM,GAA2B,EAAE,CAAC;QAC1C,MAAM,YAAY,GAA2B,EAAE,CAAC;QAEhD,KAAK,MAAM,MAAM,IAAI,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACrD,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/E,CAAC;QAED,OAAO;YACL,KAAK,EAAE,WAAW,CAAC,MAAM;YACzB,MAAM;YACN,YAAY;SACb,CAAC;IACJ,CAAC;CACF;AAED,qCAAqC;AACrC,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAC,OAAuB,EAAiB,EAAE;IAC5E,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC;AACpC,CAAC,CAAC"}