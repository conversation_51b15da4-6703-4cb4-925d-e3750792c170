/**
 * Memory Manager - Core memory operations for the learning POC
 */
import type { MemoryManager as IMemoryManager, MemoryEntry, TrainingMemory, ReflectionMemory, MemoryStorage, MemorySearchResult, MemoryImportance } from './types.js';
export declare class MemoryManager implements IMemoryManager {
    private storage;
    constructor(storage?: MemoryStorage);
    storeTraining(content: string, isRule: boolean, metadata?: Record<string, unknown>): Promise<string>;
    storeReflection(insights: readonly string[], rules: readonly string[], confidence: number): Promise<string>;
    storeConversation(content: string, importance?: MemoryImportance): Promise<string>;
    searchRelevant(query: string, limit?: number): Promise<readonly MemorySearchResult[]>;
    getTrainingMemories(): Promise<readonly TrainingMemory[]>;
    getReflections(): Promise<readonly ReflectionMemory[]>;
    getMemoryCount(): Promise<number>;
    clear(): Promise<void>;
    getMemoriesByType(type: MemoryEntry['type']): Promise<readonly MemoryEntry[]>;
    getRecentMemories(limit?: number): Promise<readonly MemoryEntry[]>;
    searchByTags(tags: readonly string[]): Promise<readonly MemorySearchResult[]>;
    getMemoryStats(): Promise<{
        total: number;
        byType: Record<string, number>;
        byImportance: Record<string, number>;
    }>;
}
export declare const createMemoryManager: (storage?: MemoryStorage) => MemoryManager;
//# sourceMappingURL=MemoryManager.d.ts.map