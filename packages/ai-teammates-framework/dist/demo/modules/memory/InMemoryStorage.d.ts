/**
 * Simple in-memory storage implementation for the POC
 * In production, this would be replaced with pgvector, Redis, etc.
 */
import type { MemoryEntry, MemoryStorage, MemoryQuery, MemorySearchResult, MemoryType } from './types.js';
export declare class InMemoryStorage implements MemoryStorage {
    private memories;
    store(memory: MemoryEntry): Promise<void>;
    retrieve(id: string): Promise<MemoryEntry | null>;
    search(query: MemoryQuery): Promise<readonly MemorySearchResult[]>;
    list(type?: MemoryType): Promise<readonly MemoryEntry[]>;
    delete(id: string): Promise<void>;
    clear(): Promise<void>;
    getSize(): number;
    getMemoryTypes(): Record<MemoryType, number>;
}
//# sourceMappingURL=InMemoryStorage.d.ts.map