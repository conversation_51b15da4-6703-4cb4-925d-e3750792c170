/**
 * Reflection Engine - Processes learned content and extracts insights
 * For POC: Uses simple pattern matching. In production: Would use LLM calls
 */
import type { MemoryManager } from '../memory/index.js';
import type { ReflectionEngine as IReflectionEngine, ReflectionInput, ReflectionResult, ExtractedInsight } from './types.js';
import type { TrainingMemory } from '../memory/types.js';
export declare class ReflectionEngine implements IReflectionEngine {
    private memoryManager;
    constructor(memoryManager: MemoryManager);
    reflect(input: ReflectionInput): Promise<ReflectionResult>;
    generateSummary(memories: readonly TrainingMemory[]): Promise<string>;
    extractRules(memories: readonly TrainingMemory[]): Promise<readonly string[]>;
    calculateConfidence(insights: readonly ExtractedInsight[]): number;
    private extractInsights;
    private normalizeRule;
    private generateRecommendations;
}
export declare const createReflectionEngine: (memoryManager: MemoryManager) => ReflectionEngine;
//# sourceMappingURL=ReflectionEngine.d.ts.map