/**
 * Reflection engine types
 */
import type { TrainingMemory } from '../memory/types.js';
export interface ReflectionInput {
    readonly trainingMemories: readonly TrainingMemory[];
    readonly agentIdentity: {
        readonly name: string;
        readonly role: string;
        readonly mission: string;
    };
    readonly context?: string;
}
export interface ExtractedInsight {
    readonly type: 'rule' | 'principle' | 'exception' | 'pattern';
    readonly content: string;
    readonly confidence: number;
    readonly supportingMemories: readonly string[];
    readonly category?: string;
}
export interface ReflectionResult {
    readonly insights: readonly ExtractedInsight[];
    readonly extractedRules: readonly string[];
    readonly overallConfidence: number;
    readonly summary: string;
    readonly recommendations: readonly string[];
    readonly memoryId: string;
}
export interface ReflectionEngine {
    reflect(input: ReflectionInput): Promise<ReflectionResult>;
    generateSummary(memories: readonly TrainingMemory[]): Promise<string>;
    extractRules(memories: readonly TrainingMemory[]): Promise<readonly string[]>;
    calculateConfidence(insights: readonly ExtractedInsight[]): number;
}
//# sourceMappingURL=types.d.ts.map