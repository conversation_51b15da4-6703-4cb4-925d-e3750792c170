{"version": 3, "file": "ReflectionEngine.js", "sourceRoot": "", "sources": ["../../../../demo/modules/reflection/ReflectionEngine.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAWH,uDAAuD;AACvD,MAAM,aAAa,GAAG;IACpB;QACE,OAAO,EAAE,sCAAsC;QAC/C,IAAI,EAAE,MAAe;QACrB,QAAQ,EAAE,YAAY;KACvB;IACD;QACE,OAAO,EAAE,4BAA4B;QACrC,IAAI,EAAE,MAAe;QACrB,QAAQ,EAAE,eAAe;KAC1B;IACD;QACE,OAAO,EAAE,mBAAmB;QAC5B,IAAI,EAAE,MAAe;QACrB,QAAQ,EAAE,YAAY;KACvB;IACD;QACE,OAAO,EAAE,iBAAiB;QAC1B,IAAI,EAAE,WAAoB;QAC1B,QAAQ,EAAE,eAAe;KAC1B;CACF,CAAC;AAEF,MAAM,OAAO,gBAAgB;IACP;IAApB,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAEpD,KAAK,CAAC,OAAO,CAAC,KAAsB;QAClC,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,gBAAgB,KAAK,CAAC,gBAAgB,CAAC,MAAM,sBAAsB,CAAC,CAAC;QAEjF,0CAA0C;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAE9D,+BAA+B;QAC/B,MAAM,cAAc,GAAG,QAAQ;aAC5B,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,KAAK,MAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC;aAC1E,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAEnC,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAE7D,mBAAmB;QACnB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAEnE,2BAA2B;QAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QAElF,6BAA6B;QAC7B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,CACvD,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAC5B,cAAc,EACd,iBAAiB,CAClB,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,0BAA0B,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,wBAAwB,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QAE9E,OAAO;YACL,QAAQ;YACR,cAAc;YACd,iBAAiB;YACjB,OAAO;YACP,eAAe;YACf,QAAQ;SACT,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,QAAmC;QACvD,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;QACxD,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;QAEnC,MAAM,UAAU,GAAG,IAAI,GAAG,CACxB,QAAQ;aACL,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAkB,CAAC;aACvC,MAAM,CAAC,OAAO,CAAC,CACnB,CAAC;QAEF,OAAO,WAAW,SAAS,eAAe,UAAU,0BAA0B;YACvE,WAAW,UAAU,CAAC,IAAI,uBAAuB,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;YACtF,sDAAsD,CAAC;IAChE,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAAmC;QACpD,OAAO,QAAQ;aACZ,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;aACrB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAED,mBAAmB,CAAC,QAAqC;QACvD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEpC,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACvF,OAAO,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC3C,CAAC;IAEO,eAAe,CAAC,QAAmC;QACzD,MAAM,QAAQ,GAAuB,EAAE,CAAC;QAExC,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE,CAAC;YAC9B,sCAAsC;YACtC,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;gBACpC,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;oBACzC,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC;wBAC3C,UAAU,EAAE,GAAG,EAAE,sCAAsC;wBACvD,kBAAkB,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;wBAC/B,QAAQ,EAAE,OAAO,CAAC,QAAQ;qBAC3B,CAAC,CAAC;oBACH,MAAM,CAAC,2BAA2B;gBACpC,CAAC;YACH,CAAC;YAED,iEAAiE;YACjE,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;gBACnF,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,MAAM,CAAC,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;oBAChE,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC;oBAC3C,UAAU,EAAE,GAAG,EAAE,uCAAuC;oBACxD,kBAAkB,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC/B,QAAQ,EAAE,MAAM,CAAC,YAAY;iBAC9B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,aAAa,CAAC,OAAe;QACnC,+EAA+E;QAC/E,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC7C,CAAC;IAEO,uBAAuB,CAC7B,QAAqC,EACrC,UAAkB;QAElB,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YACrB,eAAe,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,iBAAiB,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC;QAC1E,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,eAAe,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,eAAe,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;CACF;AAED,mBAAmB;AACnB,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,aAA4B,EAAoB,EAAE;IACvF,OAAO,IAAI,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAC7C,CAAC,CAAC"}