{"version": 3, "file": "test-scenarios.js", "sourceRoot": "", "sources": ["../../../demo/examples/test-scenarios.ts"], "names": [], "mappings": "AAAA;;GAEG;AAeH,mDAAmD;AACnD,MAAM,CAAC,MAAM,cAAc,GAA4B;IACrD;QACE,EAAE,EAAE,cAAc;QAClB,KAAK,EAAE,wBAAwB;QAC/B,WAAW,EAAE,0DAA0D;QACvE,eAAe,EAAE,yFAAyF;QAC1G,OAAO,EAAE;YACP,YAAY,EAAE,aAAa;YAC3B,gBAAgB,EAAE,SAAS;YAC3B,cAAc,EAAE,QAAQ;YACxB,YAAY,EAAE,SAAS;SACxB;QACD,eAAe,EAAE;YACf,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,kEAAkE;YAC7E,YAAY,EAAE,CAAC,6BAA6B,CAAC;SAC9C;KACF;IACD;QACE,EAAE,EAAE,cAAc;QAClB,KAAK,EAAE,oBAAoB;QAC3B,WAAW,EAAE,8DAA8D;QAC3E,eAAe,EAAE,iFAAiF;QAClG,OAAO,EAAE;YACP,YAAY,EAAE,aAAa;YAC3B,gBAAgB,EAAE,MAAM;YACxB,cAAc,EAAE,QAAQ;YACxB,YAAY,EAAE,SAAS;SACxB;QACD,eAAe,EAAE;YACf,MAAM,EAAE,WAAW;YACnB,SAAS,EAAE,2DAA2D;YACtE,YAAY,EAAE,CAAC,wBAAwB,CAAC;SACzC;KACF;IACD;QACE,EAAE,EAAE,cAAc;QAClB,KAAK,EAAE,wBAAwB;QAC/B,WAAW,EAAE,2CAA2C;QACxD,eAAe,EAAE,2EAA2E;QAC5F,OAAO,EAAE;YACP,YAAY,EAAE,aAAa;YAC3B,gBAAgB,EAAE,MAAM;YACxB,cAAc,EAAE,QAAQ;YACxB,YAAY,EAAE,KAAK;SACpB;QACD,eAAe,EAAE;YACf,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,6CAA6C;YACxD,YAAY,EAAE,CAAC,oBAAoB,CAAC;SACrC;KACF;IACD;QACE,EAAE,EAAE,cAAc;QAClB,KAAK,EAAE,mBAAmB;QAC1B,WAAW,EAAE,iDAAiD;QAC9D,eAAe,EAAE,oHAAoH;QACrI,OAAO,EAAE;YACP,YAAY,EAAE,aAAa;YAC3B,gBAAgB,EAAE,mBAAmB;YACrC,cAAc,EAAE,mBAAmB;YACnC,YAAY,EAAE,SAAS;SACxB;QACD,eAAe,EAAE;YACf,MAAM,EAAE,UAAU;YAClB,SAAS,EAAE,kEAAkE;YAC7E,YAAY,EAAE,CAAC,iBAAiB,CAAC;SAClC;KACF;IACD;QACE,EAAE,EAAE,cAAc;QAClB,KAAK,EAAE,sBAAsB;QAC7B,WAAW,EAAE,0CAA0C;QACvD,eAAe,EAAE,2FAA2F;QAC5G,OAAO,EAAE;YACP,YAAY,EAAE,YAAY;YAC1B,KAAK,EAAE;gBACL,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,QAAQ,EAAE;gBAClD,EAAE,SAAS,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE;gBAC/C,EAAE,SAAS,EAAE,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE;aACtD;YACD,YAAY,EAAE,SAAS;SACxB;QACD,eAAe,EAAE;YACf,MAAM,EAAE,gBAAgB;YACxB,SAAS,EAAE,qEAAqE;YAChF,YAAY,EAAE,CAAC,6BAA6B,EAAE,yBAAyB,CAAC;SACzE;KACF;CACF,CAAC;AAEF,2CAA2C;AAC3C,MAAM,CAAC,MAAM,mBAAmB,GAA4B;IAC1D;QACE,EAAE,EAAE,UAAU;QACd,KAAK,EAAE,uBAAuB;QAC9B,WAAW,EAAE,uCAAuC;QACpD,eAAe,EAAE,4DAA4D;QAC7E,OAAO,EAAE;YACP,YAAY,EAAE,aAAa;YAC3B,gBAAgB,EAAE,MAAM;YACxB,cAAc,EAAE,QAAQ;YACxB,YAAY,EAAE,SAAS;SACxB;QACD,eAAe,EAAE;YACf,MAAM,EAAE,UAAU;YAClB,SAAS,EAAE,+DAA+D;YAC1E,YAAY,EAAE,CAAC,iBAAiB,CAAC;SAClC;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,GAAG,cAAc,EAAE,GAAG,mBAAmB,CAAU,CAAC"}