/**
 * Sample training data for the RefundBot Kai demo
 */
export interface TrainingMessage {
    readonly speaker: 'human' | 'agent';
    readonly content: string;
    readonly isTeaching?: boolean;
    readonly ruleType?: 'policy' | 'exception' | 'escalation' | 'clarification';
    readonly metadata?: Record<string, unknown>;
}
export interface TrainingSession {
    readonly sessionId: string;
    readonly title: string;
    readonly description: string;
    readonly messages: readonly TrainingMessage[];
}
export declare const REFUND_POLICY_TRAINING: TrainingSession;
export declare const EDGE_CASES_TRAINING: TrainingSession;
export declare const ALL_TRAINING_SESSIONS: readonly [TrainingSession, TrainingSession];
//# sourceMappingURL=training-data.d.ts.map