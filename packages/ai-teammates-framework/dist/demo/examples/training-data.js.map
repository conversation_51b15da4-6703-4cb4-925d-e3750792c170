{"version": 3, "file": "training-data.js", "sourceRoot": "", "sources": ["../../../demo/examples/training-data.ts"], "names": [], "mappings": "AAAA;;GAEG;AAiBH,wCAAwC;AACxC,MAAM,CAAC,MAAM,sBAAsB,GAAoB;IACrD,SAAS,EAAE,mBAAmB;IAC9B,KAAK,EAAE,8BAA8B;IACrC,WAAW,EAAE,6DAA6D;IAC1E,QAAQ,EAAE;QACR;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,oEAAoE;YAC7E,UAAU,EAAE,KAAK;SAClB;QACD;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,yEAAyE;YAClF,UAAU,EAAE,KAAK;SAClB;QACD;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,yFAAyF;YAClG,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE;gBACR,QAAQ,EAAE,YAAY;gBACtB,SAAS,EAAE,SAAS;gBACpB,MAAM,EAAE,WAAW;aACpB;SACF;QACD;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,mGAAmG;YAC5G,UAAU,EAAE,KAAK;SAClB;QACD;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,+FAA+F;YACxG,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE;gBACR,QAAQ,EAAE,eAAe;gBACzB,SAAS,EAAE,gBAAgB;gBAC3B,MAAM,EAAE,aAAa;aACtB;SACF;QACD;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,2GAA2G;YACpH,UAAU,EAAE,KAAK;SAClB;QACD;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,iHAAiH;YAC1H,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,YAAY;YACtB,QAAQ,EAAE;gBACR,QAAQ,EAAE,aAAa;gBACvB,MAAM,EAAE,mBAAmB;aAC5B;SACF;QACD;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,0FAA0F;YACnG,UAAU,EAAE,KAAK;SAClB;QACD;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,qGAAqG;YAC9G,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,WAAW;YACrB,QAAQ,EAAE;gBACR,QAAQ,EAAE,eAAe;gBACzB,SAAS,EAAE,YAAY;gBACvB,SAAS,EAAE,YAAY;aACxB;SACF;QACD;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,iGAAiG;YAC1G,UAAU,EAAE,KAAK;SAClB;KACF;CACF,CAAC;AAEF,gCAAgC;AAChC,MAAM,CAAC,MAAM,mBAAmB,GAAoB;IAClD,SAAS,EAAE,gBAAgB;IAC3B,KAAK,EAAE,+BAA+B;IACtC,WAAW,EAAE,qDAAqD;IAClE,QAAQ,EAAE;QACR;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,iGAAiG;YAC1G,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,eAAe;YACzB,QAAQ,EAAE;gBACR,QAAQ,EAAE,eAAe;gBACzB,QAAQ,EAAE,uBAAuB;aAClC;SACF;QACD;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,6GAA6G;YACtH,UAAU,EAAE,KAAK;SAClB;QACD;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,uGAAuG;YAChH,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,eAAe;YACzB,QAAQ,EAAE;gBACR,QAAQ,EAAE,gBAAgB;gBAC1B,SAAS,EAAE,yBAAyB;aACrC;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAAG;IACnC,sBAAsB;IACtB,mBAAmB;CACX,CAAC"}