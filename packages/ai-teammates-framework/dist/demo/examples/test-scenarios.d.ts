/**
 * Test scenarios for validating learned knowledge
 */
export interface TestScenario {
    readonly id: string;
    readonly title: string;
    readonly description: string;
    readonly customerMessage: string;
    readonly context: Record<string, unknown>;
    readonly expectedOutcome: {
        readonly action: 'refund' | 'no-refund' | 'escalate' | 'partial-refund';
        readonly reasoning: string;
        readonly rulesApplied: readonly string[];
    };
}
export declare const TEST_SCENARIOS: readonly TestScenario[];
export declare const EDGE_CASE_SCENARIOS: readonly TestScenario[];
export declare const ALL_SCENARIOS: readonly TestScenario[];
//# sourceMappingURL=test-scenarios.d.ts.map