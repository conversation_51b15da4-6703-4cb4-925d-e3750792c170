/**
 * Demo Agent: RefundBot Kai
 * A customer support agent that learns refund policies through conversation
 */
import { type ReadonlyConfig } from '../src/functional/index.js';
export interface AgentIdentity {
    readonly name: string;
    readonly role: string;
    readonly mission: string;
    readonly values: readonly string[];
    readonly expertise: readonly string[];
    readonly communicationStyle: string;
}
export declare const KAI_IDENTITY: AgentIdentity;
export interface AgentState {
    readonly identity: AgentIdentity;
    readonly memoryCount: number;
    readonly lastInteraction: Date | null;
    readonly learnedRules: readonly string[];
    readonly confidence: number;
}
export declare const createInitialAgentState: (identity: AgentIdentity) => AgentState;
export declare const createDemoAgentConfig: () => ReadonlyConfig;
export declare const createDemoAgent: () => import("../src/functional/index.js").FunctionalFramework;
export declare const buildAgentContext: (state: AgentState) => string;
export declare const demoAgent: import("../src/functional/index.js").FunctionalFramework;
export declare const initialState: AgentState;
//# sourceMappingURL=agent.d.ts.map