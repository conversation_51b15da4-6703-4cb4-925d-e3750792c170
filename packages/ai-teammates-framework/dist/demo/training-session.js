/**
 * Training Session Runner - Handles conversation-based teaching
 */
// Training session processor
export class TrainingSessionRunner {
    memoryManager;
    agentState;
    constructor(memoryManager, agentState) {
        this.memoryManager = memoryManager;
        this.agentState = agentState;
    }
    async runTrainingSession(session) {
        console.log(`\n🎓 Starting training session: ${session.title}`);
        console.log(`📝 Description: ${session.description}\n`);
        const memoryIds = [];
        let rulesLearned = 0;
        let messagesProcessed = 0;
        // Process each message in the session
        for (const message of session.messages) {
            await this.processMessage(message);
            messagesProcessed++;
            // Store teaching messages as training memories
            if (message.isTeaching && message.speaker === 'human') {
                const memoryId = await this.storeTrainingMessage(message);
                memoryIds.push(memoryId);
                if (message.ruleType === 'policy' || message.ruleType === 'exception') {
                    rulesLearned++;
                }
            }
            // Store agent responses as conversation memories
            if (message.speaker === 'agent') {
                await this.memoryManager.storeConversation(`Agent response: ${message.content}`, 'medium');
            }
        }
        // Update agent state
        const updatedState = await this.updateAgentState(session, rulesLearned);
        const summary = this.generateSessionSummary(session, rulesLearned, messagesProcessed);
        console.log(`\n✅ Training session completed!`);
        console.log(`📊 Messages processed: ${messagesProcessed}`);
        console.log(`🧠 Rules learned: ${rulesLearned}`);
        console.log(`💾 Memories stored: ${memoryIds.length}\n`);
        return {
            sessionId: session.sessionId,
            messagesProcessed,
            rulesLearned,
            memoryIds,
            summary,
            updatedState
        };
    }
    async processMessage(message) {
        const speaker = message.speaker === 'human' ? '👨‍🏫 Human' : '🤖 Kai';
        console.log(`${speaker}: ${message.content}`);
        if (message.isTeaching) {
            console.log(`   📚 [TEACHING: ${message.ruleType}]`);
        }
    }
    async storeTrainingMessage(message) {
        const isRule = message.ruleType === 'policy' || message.ruleType === 'exception';
        return this.memoryManager.storeTraining(message.content, isRule, {
            ruleType: message.ruleType,
            category: message.metadata?.category,
            ...message.metadata
        });
    }
    async updateAgentState(session, rulesLearned) {
        const memoryCount = await this.memoryManager.getMemoryCount();
        const newRules = this.extractRulesFromSession(session);
        return {
            ...this.agentState,
            memoryCount,
            lastInteraction: new Date(),
            learnedRules: [...this.agentState.learnedRules, ...newRules],
            confidence: Math.min(1.0, this.agentState.confidence + (rulesLearned * 0.2))
        };
    }
    extractRulesFromSession(session) {
        return session.messages
            .filter(m => m.isTeaching && (m.ruleType === 'policy' || m.ruleType === 'exception'))
            .map(m => m.content);
    }
    generateSessionSummary(session, rulesLearned, messagesProcessed) {
        return `Training session "${session.title}" completed. ` +
            `Processed ${messagesProcessed} messages and learned ${rulesLearned} rules. ` +
            `Agent is now more knowledgeable about refund policies.`;
    }
}
// Factory function
export const createTrainingSessionRunner = (memoryManager, agentState) => {
    return new TrainingSessionRunner(memoryManager, agentState);
};
// Utility function to run multiple sessions
export const runMultipleTrainingSessions = async (sessions, memoryManager, initialState) => {
    const results = [];
    let currentState = initialState;
    for (const session of sessions) {
        const runner = createTrainingSessionRunner(memoryManager, currentState);
        const result = await runner.runTrainingSession(session);
        results.push(result);
        currentState = result.updatedState;
    }
    return {
        results,
        finalState: currentState
    };
};
//# sourceMappingURL=training-session.js.map