#!/usr/bin/env node
/**
 * Main Demo Runner - Memory-Based AI Learning POC
 *
 * This demonstrates an AI agent that:
 * 1. Learns through conversation
 * 2. Reflects on learned knowledge
 * 3. Applies knowledge to new scenarios
 * 4. Explains its reasoning
 */
import { initialState } from './agent.js';
import { createMemoryManager } from './modules/memory/index.js';
import { createReflectionEngine } from './modules/reflection/index.js';
import { runMultipleTrainingSessions } from './training-session.js';
import { createScenarioRunner } from './scenario-runner.js';
import { ALL_TRAINING_SESSIONS } from './examples/training-data.js';
import { TEST_SCENARIOS } from './examples/test-scenarios.js';
async function runFullDemo() {
    console.log('🧠 MEMORY-BASED AI LEARNING POC');
    console.log('=====================================\n');
    // Initialize components
    console.log('🔧 Initializing components...');
    const memoryManager = createMemoryManager();
    const reflectionEngine = createReflectionEngine(memoryManager);
    let currentState = initialState;
    console.log('✅ Components initialized\n');
    // Phase 1: Training
    console.log('📚 PHASE 1: TRAINING');
    console.log('====================');
    const trainingResults = await runMultipleTrainingSessions(ALL_TRAINING_SESSIONS, memoryManager, currentState);
    currentState = trainingResults.finalState;
    console.log('\n📊 Training Summary:');
    console.log(`   Sessions completed: ${trainingResults.results.length}`);
    console.log(`   Total rules learned: ${currentState.learnedRules.length}`);
    console.log(`   Agent confidence: ${Math.round(currentState.confidence * 100)}%`);
    // Phase 2: Reflection
    console.log('\n\n🤔 PHASE 2: REFLECTION');
    console.log('======================');
    const trainingMemories = await memoryManager.getTrainingMemories();
    const reflectionResult = await reflectionEngine.reflect({
        trainingMemories,
        agentIdentity: {
            name: currentState.identity.name,
            role: currentState.identity.role,
            mission: currentState.identity.mission
        }
    });
    console.log('\n💡 Reflection Summary:');
    console.log(`   Insights extracted: ${reflectionResult.insights.length}`);
    console.log(`   Rules identified: ${reflectionResult.extractedRules.length}`);
    console.log(`   Overall confidence: ${Math.round(reflectionResult.overallConfidence * 100)}%`);
    console.log(`   Summary: ${reflectionResult.summary}`);
    // Phase 3: Scenario Testing
    console.log('\n\n🧪 PHASE 3: SCENARIO TESTING');
    console.log('============================');
    const scenarioRunner = createScenarioRunner(memoryManager, currentState);
    const evaluationSummary = await scenarioRunner.runMultipleScenarios(TEST_SCENARIOS);
    // Phase 4: Success Criteria Validation
    console.log('\n\n✅ PHASE 4: SUCCESS CRITERIA VALIDATION');
    console.log('=======================================');
    const criteria = validateSuccessCriteria(trainingResults, reflectionResult, evaluationSummary);
    const success = criteria.rulesLearned >= 2 &&
        criteria.rulesAppliedCorrectly >= 2 &&
        criteria.canArticulateReasoning &&
        criteria.modulesDecoupled;
    console.log('\n🎯 SUCCESS CRITERIA:');
    console.log(`   ✅ AI learned 2+ rules via chat: ${criteria.rulesLearned >= 2 ? 'PASS' : 'FAIL'} (${criteria.rulesLearned} rules)`);
    console.log(`   ✅ AI applied rules in test scenarios: ${criteria.rulesAppliedCorrectly >= 2 ? 'PASS' : 'FAIL'} (${criteria.rulesAppliedCorrectly} correct)`);
    console.log(`   ✅ AI articulated decision paths: ${criteria.canArticulateReasoning ? 'PASS' : 'FAIL'}`);
    console.log(`   ✅ Modules are decoupled/testable: ${criteria.modulesDecoupled ? 'PASS' : 'FAIL'}`);
    console.log(`\n🏆 OVERALL RESULT: ${success ? '🎉 SUCCESS!' : '❌ NEEDS WORK'}`);
    const summary = generateSummary(success, criteria, evaluationSummary);
    console.log(`\n📝 ${summary}`);
    return {
        success,
        criteria,
        summary,
        details: {
            trainingResults,
            reflectionResult,
            evaluationSummary
        }
    };
}
function validateSuccessCriteria(trainingResults, reflectionResult, evaluationSummary) {
    // Count rules learned
    const rulesLearned = reflectionResult.extractedRules.length;
    // Count correct applications
    const rulesAppliedCorrectly = evaluationSummary.correctDecisions;
    // Check if agent can articulate reasoning
    const canArticulateReasoning = evaluationSummary.results.every((result) => result.agentDecision.reasoning && result.agentDecision.reasoning.length > 10);
    // Modules are decoupled by design (this is architectural)
    const modulesDecoupled = true;
    return {
        rulesLearned,
        rulesAppliedCorrectly,
        canArticulateReasoning,
        modulesDecoupled
    };
}
function generateSummary(success, criteria, evaluationSummary) {
    if (success) {
        return `POC SUCCESSFUL! The agent learned ${criteria.rulesLearned} rules through conversation, ` +
            `applied them correctly in ${criteria.rulesAppliedCorrectly}/${evaluationSummary.totalScenarios} scenarios, ` +
            `and provided clear reasoning for all decisions. The modular architecture enables ` +
            `easy testing and extension. Ready for production development!`;
    }
    else {
        return `POC needs improvement. While the architecture works, the agent needs more training ` +
            `or refinement to meet all success criteria. Consider additional training sessions ` +
            `or improved reflection algorithms.`;
    }
}
// Run the demo if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runFullDemo()
        .then(result => {
        process.exit(result.success ? 0 : 1);
    })
        .catch(error => {
        console.error('❌ Demo failed:', error);
        process.exit(1);
    });
}
export { runFullDemo };
//# sourceMappingURL=main.js.map