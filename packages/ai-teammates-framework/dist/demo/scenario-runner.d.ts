/**
 * <PERSON><PERSON>rio Runner - Tests agent's ability to apply learned knowledge
 */
import type { AgentState } from './agent.js';
import type { MemoryManager, MemorySearchResult } from './modules/memory/index.js';
import type { TestScenario } from './examples/test-scenarios.js';
export interface AgentDecision {
    readonly action: 'refund' | 'no-refund' | 'escalate' | 'partial-refund' | 'uncertain';
    readonly reasoning: string;
    readonly confidence: number;
    readonly rulesApplied: readonly string[];
    readonly memoryReferences: readonly string[];
}
export interface ScenarioResult {
    readonly scenarioId: string;
    readonly customerMessage: string;
    readonly agentDecision: AgentDecision;
    readonly expectedOutcome: TestScenario['expectedOutcome'];
    readonly isCorrect: boolean;
    readonly memoryRetrieved: readonly MemorySearchResult[];
    readonly processingLog: readonly string[];
}
export interface EvaluationSummary {
    readonly totalScenarios: number;
    readonly correctDecisions: number;
    readonly accuracy: number;
    readonly results: readonly Sc<PERSON>ult[];
    readonly insights: readonly string[];
}
export declare class ScenarioRunner {
    private memoryManager;
    private agentState;
    constructor(memoryManager: MemoryManager, agentState: AgentState);
    runScenario(scenario: TestScenario): Promise<ScenarioResult>;
    runMultipleScenarios(scenarios: readonly TestScenario[]): Promise<EvaluationSummary>;
    private makeDecision;
    private extractDeliveryDays;
    private checkIfDamaged;
    private isComplexScenario;
    private evaluateDecision;
    private generateInsights;
}
export declare const createScenarioRunner: (memoryManager: MemoryManager, agentState: AgentState) => ScenarioRunner;
//# sourceMappingURL=scenario-runner.d.ts.map