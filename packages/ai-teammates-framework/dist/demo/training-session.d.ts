/**
 * Training Session Runner - Handles conversation-based teaching
 */
import type { AgentState } from './agent.js';
import type { MemoryManager } from './modules/memory/index.js';
import type { TrainingSession } from './examples/training-data.js';
export interface TrainingResult {
    readonly sessionId: string;
    readonly messagesProcessed: number;
    readonly rulesLearned: number;
    readonly memoryIds: readonly string[];
    readonly summary: string;
    readonly updatedState: AgentState;
}
export declare class TrainingSessionRunner {
    private memoryManager;
    private agentState;
    constructor(memoryManager: MemoryManager, agentState: AgentState);
    runTrainingSession(session: TrainingSession): Promise<TrainingResult>;
    private processMessage;
    private storeTrainingMessage;
    private updateAgentState;
    private extractRulesFromSession;
    private generateSessionSummary;
}
export declare const createTrainingSessionRunner: (memoryManager: MemoryManager, agentState: AgentState) => TrainingSessionRunner;
export declare const runMultipleTrainingSessions: (sessions: readonly TrainingSession[], memoryManager: MemoryManager, initialState: AgentState) => Promise<{
    results: readonly TrainingResult[];
    finalState: AgentState;
}>;
//# sourceMappingURL=training-session.d.ts.map